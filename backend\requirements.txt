# FastAPI框架及相关依赖
fastapi>=0.68.0,<0.69.0
uvicorn>=0.15.0,<0.16.0
pydantic>=1.8.0,<1.9.0
email-validator>=1.1.3,<1.2.0

# 系统监控
psutil>=5.8.0,<5.9.0

# 数据处理
python-multipart>=0.0.5,<0.1.0
python-dotenv>=0.19.0,<0.20.0

# MongoDB数据库驱动
pymongo>=3.12.0,<3.13.0
motor>=2.5.0,<2.6.0

# 工具库
python-dateutil>=2.8.2,<2.9.0
requests>=2.26.0,<2.27.0

# 跨域支持
starlette>=0.14.2,<0.15.0

# 注意：以下模型相关依赖需要根据实际情况添加
# 自然语言处理（如果需要）
# nltk>=3.6.5,<3.7.0
# spacy>=3.1.3,<3.2.0
# transformers>=4.11.3,<4.12.0

# 机器学习（如果需要）
# scikit-learn>=1.0.0,<1.1.0
# tensorflow>=2.6.0,<2.7.0
# torch>=1.9.0,<1.10.0