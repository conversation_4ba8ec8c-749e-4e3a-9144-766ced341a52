from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime

# 情绪分析请求模型
class AnalysisRequest(BaseModel):
    """情绪分析请求模型"""
    content: str = Field(..., description="用户输入的文本内容")
    user_id: Optional[str] = Field(None, description="用户ID，可选")
    metadata: Optional[Dict[str, Any]] = Field(None, description="额外的元数据信息")

# 对话记录项模型
class DialogueItem(BaseModel):
    """对话记录项模型"""
    speaker: str = Field(..., description="发言者（用户/系统）")
    content: str = Field(..., description="发言内容")
    timestamp: Optional[str] = Field(None, description="发言时间戳")

# 建议项模型
class RecommendationItem(BaseModel):
    """建议项模型"""
    title: str = Field(..., description="建议标题")
    content: str = Field(..., description="建议内容")
    category: Optional[str] = Field(None, description="建议类别")

# 抑郁风险评估模型
class DepressionRisk(BaseModel):
    """抑郁风险评估模型"""
    level: str = Field(..., description="风险等级（low/medium/high）")
    text: str = Field(..., description="风险等级文本描述")
    confidence: int = Field(..., description="评估可信度百分比")
    description: str = Field(..., description="详细描述")

# 情绪值模型
class EmotionValues(BaseModel):
    """情绪值模型"""
    positive: int = Field(..., description="积极情绪百分比")
    negative: int = Field(..., description="消极情绪百分比")
    neutral: int = Field(..., description="中性情绪百分比")

# 分析结果模型
class AnalysisResult(BaseModel):
    """分析结果模型"""
    id: str = Field(..., description="分析结果唯一ID")
    timestamp: str = Field(..., description="分析时间戳")
    content: str = Field(..., description="用户输入的原始内容")
    emotion_values: EmotionValues = Field(..., description="情绪值分布")
    emotion_summary: str = Field(..., description="情绪分析摘要")
    depression_risk: DepressionRisk = Field(..., description="抑郁风险评估")
    dialogue_record: List[Dict[str, str]] = Field(..., description="对话记录")
    recommendations: List[Dict[str, str]] = Field(..., description="建议列表")
    scores: Dict[str, int] = Field(..., description="各项评分")

# 分析响应模型
class AnalysisResponse(BaseModel):
    """分析响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    result: Optional[AnalysisResult] = Field(None, description="分析结果")

# 历史记录项模型
class HistoryItem(BaseModel):
    """历史记录项模型"""
    id: str = Field(..., description="分析ID")
    timestamp: str = Field(..., description="分析时间")
    content_preview: str = Field(..., description="内容预览")
    emotion_summary: str = Field(..., description="情绪摘要")

# 历史记录响应模型
class HistoryResponse(BaseModel):
    """历史记录响应模型"""
    success: bool = Field(..., description="请求是否成功")
    message: str = Field(..., description="响应消息")
    history: List[HistoryItem] = Field(..., description="历史记录列表")
    total: int = Field(..., description="总记录数")