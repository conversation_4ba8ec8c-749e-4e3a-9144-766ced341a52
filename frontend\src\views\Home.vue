<template>
  <div class="home-container">
    <!-- 顶部Logo区域 -->
    <div class="logo-area">
      <div class="logo">LOGO</div>
    </div>
    
    <!-- 中间内容区域 -->
    <div class="content-area">
      <div class="welcome-box">
        <h1 class="title">情绪分析助手</h1>
        <p class="subtitle">一站式情绪管理</p>
        <el-button type="primary" size="large" @click="startAnalysis" class="start-btn">
          开始分析
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HomePage',
  methods: {
    // 开始分析按钮点击事件
    startAnalysis() {
      this.$router.push('/analysis');
    }
  }
};
</script>

<style scoped>
.home-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.logo-area {
  padding: 20px;
  display: flex;
  justify-content: flex-start;
}

.logo {
  border: 1px solid #ddd;
  padding: 10px 20px;
  font-size: 18px;
  font-weight: bold;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-area {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.welcome-box {
  text-align: center;
  background-color: white;
  padding: 50px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  width: 100%;
}

.title {
  font-size: 32px;
  margin-bottom: 16px;
  color: #333;
}

.subtitle {
  font-size: 18px;
  color: #666;
  margin-bottom: 40px;
}

.start-btn {
  padding: 12px 30px;
  font-size: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.start-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.start-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.start-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.start-btn:hover::before {
  left: 100%;
}
</style>