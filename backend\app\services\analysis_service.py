from typing import List, Dict, Any, Optional
import json
from datetime import datetime
import logging

from app.models.conversation import ConversationAnalysis, ConversationMessage
from app.models.virtual_patient import VirtualPatientPrompt
from app.services.database import get_database
from app.services.conversation_service import conversation_service
from app.services.virtual_patient_service import virtual_patient_service

logger = logging.getLogger(__name__)

class AnalysisService:
    """分析评估服务类"""
    
    def __init__(self):
        self.db = None
    
    async def _get_db(self):
        """获取数据库连接"""
        if not self.db:
            db_service = await get_database()
            self.db = db_service.database
        return self.db
    
    async def analyze_conversation(self, session_id: str) -> ConversationAnalysis:
        """分析对话并生成评估报告"""
        try:
            # 获取对话历史
            messages = await conversation_service.get_conversation_history(session_id)
            if not messages:
                raise Exception("没有找到对话记录")
            
            # 获取会话信息
            session = await conversation_service.get_session(session_id)
            if not session:
                raise Exception("会话不存在")
            
            # 获取患者信息
            patient = await self._get_patient_info(session.patient_id)
            if not patient:
                raise Exception("患者信息不存在")
            
            # 执行各项分析
            emotional_analysis = await self._analyze_emotional_value(messages, patient)
            diagnosis_analysis = await self._analyze_diagnosis_accuracy(messages, patient)
            department_analysis = await self._analyze_department_accuracy(messages, patient)
            
            # 计算总体得分
            overall_score = (
                emotional_analysis["score"] * 0.3 +
                diagnosis_analysis["score"] * 0.4 +
                department_analysis["score"] * 0.3
            )
            
            # 生成建议和反馈
            recommendations = await self._generate_recommendations(
                emotional_analysis, diagnosis_analysis, department_analysis
            )
            
            # 生成对话摘要
            conversation_summary = await self._generate_conversation_summary(messages)
            
            # 识别关键时刻
            key_moments = await self._identify_key_moments(messages, patient)
            
            # 创建分析结果
            analysis = ConversationAnalysis(
                session_id=session_id,
                emotional_value_score=emotional_analysis["score"],
                diagnosis_accuracy_score=diagnosis_analysis["score"],
                department_accuracy_score=department_analysis["score"],
                overall_score=overall_score,
                emotional_analysis=emotional_analysis,
                diagnosis_analysis=diagnosis_analysis,
                department_analysis=department_analysis,
                recommendations=recommendations["recommendations"],
                strengths=recommendations["strengths"],
                weaknesses=recommendations["weaknesses"],
                conversation_summary=conversation_summary,
                key_moments=key_moments
            )
            
            # 保存分析结果
            db = await self._get_db()
            await db.conversation_analyses.insert_one(analysis.dict())
            
            logger.info(f"对话分析完成: {session_id}")
            return analysis
            
        except Exception as e:
            logger.error(f"对话分析失败: {str(e)}")
            raise
    
    async def _get_patient_info(self, patient_id: str) -> Optional[VirtualPatientPrompt]:
        """获取患者信息"""
        try:
            db = await self._get_db()
            patient_data = await db.virtual_patients.find_one({"patient_id": patient_id})
            
            if patient_data:
                patient_data.pop('_id', None)
                return VirtualPatientPrompt(**patient_data)
            return None
            
        except Exception as e:
            logger.error(f"获取患者信息失败: {str(e)}")
            return None
    
    async def _analyze_emotional_value(self, messages: List[ConversationMessage], 
                                     patient: VirtualPatientPrompt) -> Dict[str, Any]:
        """分析情绪价值给予"""
        # TODO: 调用AI模型分析医生的情绪支持能力
        
        # 模拟分析逻辑
        doctor_messages = [msg for msg in messages if msg.message_type.value == "doctor"]
        
        # 分析医生的共情能力、安慰技巧等
        empathy_score = 75  # 模拟得分
        comfort_score = 80
        communication_score = 85
        
        overall_score = (empathy_score + comfort_score + communication_score) / 3
        
        return {
            "score": overall_score,
            "empathy_score": empathy_score,
            "comfort_score": comfort_score,
            "communication_score": communication_score,
            "details": {
                "empathy_examples": ["医生表现出了对患者担忧的理解"],
                "comfort_techniques": ["使用了安慰性语言", "给出了积极的建议"],
                "communication_quality": "沟通清晰，语言温和"
            }
        }
    
    async def _analyze_diagnosis_accuracy(self, messages: List[ConversationMessage], 
                                        patient: VirtualPatientPrompt) -> Dict[str, Any]:
        """分析诊断正确性"""
        # TODO: 调用AI模型分析医生的诊断准确性
        
        doctor_messages = [msg for msg in messages if msg.message_type.value == "doctor"]
        
        # 检查是否询问了关键症状
        key_symptoms_asked = 0
        total_key_symptoms = len(patient.symptoms)
        
        # 模拟检查逻辑
        for symptom in patient.symptoms:
            for msg in doctor_messages:
                if symptom in msg.content:
                    key_symptoms_asked += 1
                    break
        
        # 计算诊断准确性得分
        symptom_coverage = (key_symptoms_asked / total_key_symptoms) * 100 if total_key_symptoms > 0 else 0
        diagnostic_reasoning = 75  # 模拟诊断推理得分
        
        overall_score = (symptom_coverage + diagnostic_reasoning) / 2
        
        return {
            "score": overall_score,
            "symptom_coverage": symptom_coverage,
            "diagnostic_reasoning": diagnostic_reasoning,
            "correct_diagnosis": patient.correct_diagnosis,
            "key_symptoms_asked": key_symptoms_asked,
            "total_key_symptoms": total_key_symptoms,
            "details": {
                "missed_symptoms": [s for s in patient.symptoms if s not in " ".join([m.content for m in doctor_messages])],
                "diagnostic_approach": "系统性询问，逻辑清晰"
            }
        }
    
    async def _analyze_department_accuracy(self, messages: List[ConversationMessage], 
                                         patient: VirtualPatientPrompt) -> Dict[str, Any]:
        """分析科室分配正确性"""
        # TODO: 调用AI模型分析科室分配的准确性
        
        correct_department = patient.correct_department.value
        
        # 模拟检查医生是否提到了正确的科室
        doctor_messages = [msg for msg in messages if msg.message_type.value == "doctor"]
        department_mentioned = False
        
        department_keywords = {
            "psychiatry": ["精神科", "心理科", "心理医生"],
            "cardiology": ["心内科", "心脏科", "心血管科"],
            "neurology": ["神经科", "神经内科"],
            "internal_medicine": ["内科", "全科"],
            "emergency": ["急诊科", "急诊"]
        }
        
        mentioned_departments = []
        for msg in doctor_messages:
            for dept, keywords in department_keywords.items():
                if any(keyword in msg.content for keyword in keywords):
                    mentioned_departments.append(dept)
                    if dept == correct_department:
                        department_mentioned = True
        
        # 计算得分
        accuracy_score = 100 if department_mentioned else 0
        reasoning_score = 80  # 模拟推理过程得分
        
        overall_score = (accuracy_score + reasoning_score) / 2
        
        return {
            "score": overall_score,
            "accuracy_score": accuracy_score,
            "reasoning_score": reasoning_score,
            "correct_department": correct_department,
            "department_mentioned": department_mentioned,
            "mentioned_departments": mentioned_departments,
            "details": {
                "referral_reasoning": "基于症状特点进行科室推荐",
                "alternative_departments": mentioned_departments
            }
        }
    
    async def _generate_recommendations(self, emotional_analysis: Dict, 
                                      diagnosis_analysis: Dict, 
                                      department_analysis: Dict) -> Dict[str, List[str]]:
        """生成改进建议"""
        recommendations = []
        strengths = []
        weaknesses = []
        
        # 基于情绪分析生成建议
        if emotional_analysis["score"] < 70:
            recommendations.append("建议加强共情能力，多关注患者的情绪状态")
            weaknesses.append("情绪支持不够充分")
        else:
            strengths.append("很好地给予了患者情绪支持")
        
        # 基于诊断分析生成建议
        if diagnosis_analysis["score"] < 70:
            recommendations.append("建议更系统地询问患者症状，提高诊断准确性")
            weaknesses.append("症状询问不够全面")
        else:
            strengths.append("诊断思路清晰，询问全面")
        
        # 基于科室分析生成建议
        if department_analysis["score"] < 70:
            recommendations.append("建议根据患者症状特点，准确推荐相应科室")
            weaknesses.append("科室推荐需要改进")
        else:
            strengths.append("科室推荐准确")
        
        return {
            "recommendations": recommendations,
            "strengths": strengths,
            "weaknesses": weaknesses
        }
    
    async def _generate_conversation_summary(self, messages: List[ConversationMessage]) -> str:
        """生成对话摘要"""
        # TODO: 调用AI模型生成对话摘要
        
        doctor_count = len([msg for msg in messages if msg.message_type.value == "doctor"])
        patient_count = len([msg for msg in messages if msg.message_type.value == "patient"])
        
        return f"本次对话共进行了{len(messages)}轮交流，医生提问{doctor_count}次，患者回应{patient_count}次。医生通过系统性询问了解了患者的主要症状和病史，给出了相应的建议和指导。"
    
    async def _identify_key_moments(self, messages: List[ConversationMessage], 
                                  patient: VirtualPatientPrompt) -> List[Dict[str, Any]]:
        """识别对话中的关键时刻"""
        key_moments = []
        
        # 识别症状询问的关键时刻
        for i, msg in enumerate(messages):
            if msg.message_type.value == "doctor":
                for symptom in patient.symptoms:
                    if symptom in msg.content:
                        key_moments.append({
                            "round": msg.round_number,
                            "type": "symptom_inquiry",
                            "description": f"医生询问了关键症状：{symptom}",
                            "message": msg.content
                        })
                        break
        
        return key_moments[:5]  # 返回前5个关键时刻

# 全局服务实例
analysis_service = AnalysisService()
