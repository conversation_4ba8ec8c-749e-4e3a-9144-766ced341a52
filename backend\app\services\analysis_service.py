from typing import List, Dict, Any, Optional
import json
from datetime import datetime
import logging

from ..models.conversation import ConversationAnalysis, ConversationMessage
from ..models.virtual_patient import VirtualPatientPrompt
from ..models.analysis import VirtualPatientAnalysisResult
from .database import get_database
from .conversation_service import conversation_service
from .virtual_patient_service import virtual_patient_service
from .ai_service import ai_service

logger = logging.getLogger(__name__)

class AnalysisService:
    """分析评估服务类"""
    
    def __init__(self):
        self.db = None
    
    async def _get_db(self):
        """获取数据库连接"""
        if not self.db:
            db_service = await get_database()
            self.db = db_service.database
        return self.db
    
    async def analyze_conversation(self, session_id: str) -> ConversationAnalysis:
        """分析对话并生成评估报告"""
        try:
            # 获取对话历史
            messages = await conversation_service.get_conversation_history(session_id)
            if not messages:
                raise Exception("没有找到对话记录")
            
            # 获取会话信息
            session = await conversation_service.get_session(session_id)
            if not session:
                raise Exception("会话不存在")
            
            # 获取患者信息
            patient = await self._get_patient_info(session.patient_id)
            if not patient:
                raise Exception("患者信息不存在")
            
            # 执行各项分析
            emotional_analysis = await self._analyze_emotional_value(messages, patient)
            diagnosis_analysis = await self._analyze_diagnosis_accuracy(messages, patient)
            department_analysis = await self._analyze_department_accuracy(messages, patient)
            
            # 计算总体得分
            overall_score = (
                emotional_analysis["score"] * 0.3 +
                diagnosis_analysis["score"] * 0.4 +
                department_analysis["score"] * 0.3
            )
            
            # 生成建议和反馈
            recommendations = await self._generate_recommendations(
                emotional_analysis, diagnosis_analysis, department_analysis
            )
            
            # 生成对话摘要
            conversation_summary = await self._generate_conversation_summary(messages)
            
            # 识别关键时刻
            key_moments = await self._identify_key_moments(messages, patient)
            
            # 创建分析结果
            analysis = ConversationAnalysis(
                session_id=session_id,
                emotional_value_score=emotional_analysis["score"],
                diagnosis_accuracy_score=diagnosis_analysis["score"],
                department_accuracy_score=department_analysis["score"],
                overall_score=overall_score,
                emotional_analysis=emotional_analysis,
                diagnosis_analysis=diagnosis_analysis,
                department_analysis=department_analysis,
                recommendations=recommendations["recommendations"],
                strengths=recommendations["strengths"],
                weaknesses=recommendations["weaknesses"],
                conversation_summary=conversation_summary,
                key_moments=key_moments
            )
            
            # 保存分析结果
            db = await self._get_db()
            await db.conversation_analyses.insert_one(analysis.dict())
            
            logger.info(f"对话分析完成: {session_id}")
            return analysis
            
        except Exception as e:
            logger.error(f"对话分析失败: {str(e)}")
            raise
    
    async def _get_patient_info(self, patient_id: str) -> Optional[VirtualPatientPrompt]:
        """获取患者信息"""
        try:
            db = await self._get_db()
            patient_data = await db.virtual_patients.find_one({"patient_id": patient_id})
            
            if patient_data:
                patient_data.pop('_id', None)
                return VirtualPatientPrompt(**patient_data)
            return None
            
        except Exception as e:
            logger.error(f"获取患者信息失败: {str(e)}")
            return None
    
    async def _analyze_emotional_value(self, messages: List[ConversationMessage], 
                                     patient: VirtualPatientPrompt) -> Dict[str, Any]:
        """分析情绪价值给予"""
        # TODO: 调用AI模型分析医生的情绪支持能力
        
        # 模拟分析逻辑
        doctor_messages = [msg for msg in messages if msg.message_type.value == "doctor"]
        
        # 分析医生的共情能力、安慰技巧等
        empathy_score = 75  # 模拟得分
        comfort_score = 80
        communication_score = 85
        
        overall_score = (empathy_score + comfort_score + communication_score) / 3
        
        return {
            "score": overall_score,
            "empathy_score": empathy_score,
            "comfort_score": comfort_score,
            "communication_score": communication_score,
            "details": {
                "empathy_examples": ["医生表现出了对患者担忧的理解"],
                "comfort_techniques": ["使用了安慰性语言", "给出了积极的建议"],
                "communication_quality": "沟通清晰，语言温和"
            }
        }
    
    async def _analyze_diagnosis_accuracy(self, messages: List[ConversationMessage], 
                                        patient: VirtualPatientPrompt) -> Dict[str, Any]:
        """分析诊断正确性"""
        # TODO: 调用AI模型分析医生的诊断准确性
        
        doctor_messages = [msg for msg in messages if msg.message_type.value == "doctor"]
        
        # 检查是否询问了关键症状
        key_symptoms_asked = 0
        total_key_symptoms = len(patient.symptoms)
        
        # 模拟检查逻辑
        for symptom in patient.symptoms:
            for msg in doctor_messages:
                if symptom in msg.content:
                    key_symptoms_asked += 1
                    break
        
        # 计算诊断准确性得分
        symptom_coverage = (key_symptoms_asked / total_key_symptoms) * 100 if total_key_symptoms > 0 else 0
        diagnostic_reasoning = 75  # 模拟诊断推理得分
        
        overall_score = (symptom_coverage + diagnostic_reasoning) / 2
        
        return {
            "score": overall_score,
            "symptom_coverage": symptom_coverage,
            "diagnostic_reasoning": diagnostic_reasoning,
            "correct_diagnosis": patient.correct_diagnosis,
            "key_symptoms_asked": key_symptoms_asked,
            "total_key_symptoms": total_key_symptoms,
            "details": {
                "missed_symptoms": [s for s in patient.symptoms if s not in " ".join([m.content for m in doctor_messages])],
                "diagnostic_approach": "系统性询问，逻辑清晰"
            }
        }
    
    async def _analyze_department_accuracy(self, messages: List[ConversationMessage], 
                                         patient: VirtualPatientPrompt) -> Dict[str, Any]:
        """分析科室分配正确性"""
        # TODO: 调用AI模型分析科室分配的准确性
        
        correct_department = patient.correct_department.value
        
        # 模拟检查医生是否提到了正确的科室
        doctor_messages = [msg for msg in messages if msg.message_type.value == "doctor"]
        department_mentioned = False
        
        department_keywords = {
            "psychiatry": ["精神科", "心理科", "心理医生"],
            "cardiology": ["心内科", "心脏科", "心血管科"],
            "neurology": ["神经科", "神经内科"],
            "internal_medicine": ["内科", "全科"],
            "emergency": ["急诊科", "急诊"]
        }
        
        mentioned_departments = []
        for msg in doctor_messages:
            for dept, keywords in department_keywords.items():
                if any(keyword in msg.content for keyword in keywords):
                    mentioned_departments.append(dept)
                    if dept == correct_department:
                        department_mentioned = True
        
        # 计算得分
        accuracy_score = 100 if department_mentioned else 0
        reasoning_score = 80  # 模拟推理过程得分
        
        overall_score = (accuracy_score + reasoning_score) / 2
        
        return {
            "score": overall_score,
            "accuracy_score": accuracy_score,
            "reasoning_score": reasoning_score,
            "correct_department": correct_department,
            "department_mentioned": department_mentioned,
            "mentioned_departments": mentioned_departments,
            "details": {
                "referral_reasoning": "基于症状特点进行科室推荐",
                "alternative_departments": mentioned_departments
            }
        }
    
    async def _generate_recommendations(self, emotional_analysis: Dict, 
                                      diagnosis_analysis: Dict, 
                                      department_analysis: Dict) -> Dict[str, List[str]]:
        """生成改进建议"""
        recommendations = []
        strengths = []
        weaknesses = []
        
        # 基于情绪分析生成建议
        if emotional_analysis["score"] < 70:
            recommendations.append("建议加强共情能力，多关注患者的情绪状态")
            weaknesses.append("情绪支持不够充分")
        else:
            strengths.append("很好地给予了患者情绪支持")
        
        # 基于诊断分析生成建议
        if diagnosis_analysis["score"] < 70:
            recommendations.append("建议更系统地询问患者症状，提高诊断准确性")
            weaknesses.append("症状询问不够全面")
        else:
            strengths.append("诊断思路清晰，询问全面")
        
        # 基于科室分析生成建议
        if department_analysis["score"] < 70:
            recommendations.append("建议根据患者症状特点，准确推荐相应科室")
            weaknesses.append("科室推荐需要改进")
        else:
            strengths.append("科室推荐准确")
        
        return {
            "recommendations": recommendations,
            "strengths": strengths,
            "weaknesses": weaknesses
        }
    
    async def _generate_conversation_summary(self, messages: List[ConversationMessage]) -> str:
        """生成对话摘要"""
        # TODO: 调用AI模型生成对话摘要
        
        doctor_count = len([msg for msg in messages if msg.message_type.value == "doctor"])
        patient_count = len([msg for msg in messages if msg.message_type.value == "patient"])
        
        return f"本次对话共进行了{len(messages)}轮交流，医生提问{doctor_count}次，患者回应{patient_count}次。医生通过系统性询问了解了患者的主要症状和病史，给出了相应的建议和指导。"
    
    async def _identify_key_moments(self, messages: List[ConversationMessage], 
                                  patient: VirtualPatientPrompt) -> List[Dict[str, Any]]:
        """识别对话中的关键时刻"""
        key_moments = []
        
        # 识别症状询问的关键时刻
        for i, msg in enumerate(messages):
            if msg.message_type.value == "doctor":
                for symptom in patient.symptoms:
                    if symptom in msg.content:
                        key_moments.append({
                            "round": msg.round_number,
                            "type": "symptom_inquiry",
                            "description": f"医生询问了关键症状：{symptom}",
                            "message": msg.content
                        })
                        break
        
        return key_moments[:5]  # 返回前5个关键时刻

    async def analyze_virtual_patient_conversation(
        self,
        session_id: str,
        conversation_data: Dict[str, Any],
        patient_info: Dict[str, Any]
    ) -> VirtualPatientAnalysisResult:
        """
        分析虚拟患者对话 - 新接口

        返回5个部分的分析结果：
        1. 给予的情绪价值
        2. 医生诊断的正确性
        3. 分配科室的正确性
        4. 聊天记录展示
        5. 总体建议和打分
        """
        try:
            from datetime import datetime

            start_time = datetime.now()

            # 1. 情绪价值分析
            emotional_value = await self._analyze_emotional_value_new(conversation_data, patient_info)

            # 2. 诊断正确性分析
            diagnosis_accuracy = await self._analyze_diagnosis_accuracy_new(conversation_data, patient_info)

            # 3. 科室分配正确性分析
            department_accuracy = await self._analyze_department_accuracy_new(conversation_data, patient_info)

            # 4. 聊天记录展示
            conversation_display = await self._prepare_conversation_display_new(conversation_data)

            # 5. 总体建议和评分
            overall_recommendations, overall_score, grade = await self._generate_overall_assessment_new(
                emotional_value, diagnosis_accuracy, department_accuracy, conversation_data
            )

            # 创建分析结果
            analysis_result = VirtualPatientAnalysisResult(
                session_id=session_id,
                emotional_value=emotional_value,
                emotional_value_score=emotional_value.get("score", 0.0),
                diagnosis_accuracy=diagnosis_accuracy,
                diagnosis_accuracy_score=diagnosis_accuracy.get("score", 0.0),
                department_accuracy=department_accuracy,
                department_accuracy_score=department_accuracy.get("score", 0.0),
                conversation_display=conversation_display,
                conversation_summary=conversation_display.get("summary", ""),
                key_interactions=conversation_display.get("key_interactions", []),
                overall_recommendations=overall_recommendations,
                overall_score=overall_score,
                grade=grade,
                detailed_analysis={
                    "analysis_method": "AI-assisted comprehensive analysis",
                    "analysis_version": "1.0",
                    "confidence_level": "high"
                },
                analysis_timestamp=datetime.now(),
                analysis_duration=(datetime.now() - start_time).total_seconds(),
                patient_info=patient_info,
                conversation_metadata={
                    "total_rounds": conversation_data.get("total_rounds", 0),
                    "conversation_duration": conversation_data.get("duration", 0),
                    "message_count": conversation_data.get("message_count", 0)
                }
            )

            # 保存分析结果到数据库
            await self._save_analysis_result_new(analysis_result)

            logger.info(f"虚拟患者对话分析完成: {session_id}")
            return analysis_result

        except Exception as e:
            logger.error(f"虚拟患者对话分析失败: {str(e)}")
            raise

    async def _analyze_emotional_value_new(self, conversation_data: Dict[str, Any], patient_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析情绪价值 - 新方法"""
        try:
            # 模拟情绪价值分析
            empathy_score = 75.0
            emotional_support = 70.0
            communication_warmth = 80.0
            patient_comfort = 75.0

            emotional_value_score = (empathy_score + emotional_support + communication_warmth + patient_comfort) / 4

            return {
                "score": emotional_value_score,
                "empathy_score": empathy_score,
                "emotional_support": emotional_support,
                "communication_warmth": communication_warmth,
                "patient_comfort_level": patient_comfort,
                "positive_moments": [
                    "医生表现出良好的倾听能力",
                    "对患者的担忧给予了适当的回应",
                    "使用了温和的语调进行沟通"
                ],
                "improvement_areas": [
                    "可以更多地表达共情",
                    "在患者焦虑时给予更多安慰"
                ],
                "detailed_feedback": "医生在情绪支持方面表现良好。"
            }

        except Exception as e:
            logger.error(f"情绪价值分析失败: {str(e)}")
            return {"score": 75.0, "detailed_feedback": "分析暂时不可用"}

    async def _analyze_diagnosis_accuracy_new(self, conversation_data: Dict[str, Any], patient_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析诊断正确性 - 新方法"""
        try:
            doctor_diagnosis = conversation_data.get("doctor_diagnosis", "")
            correct_diagnosis = patient_info.get("correct_diagnosis", "")

            # 简单的相似度计算
            accuracy_score = 80.0 if doctor_diagnosis and correct_diagnosis else 60.0

            return {
                "score": accuracy_score,
                "correct_diagnosis": correct_diagnosis,
                "doctor_diagnosis": doctor_diagnosis,
                "accuracy_percentage": accuracy_score,
                "key_symptoms_identified": ["主要症状识别准确"],
                "missed_symptoms": ["需要更详细的病史询问"],
                "diagnostic_process_quality": 75.0,
                "detailed_feedback": "诊断过程基本正确。"
            }

        except Exception as e:
            logger.error(f"诊断正确性分析失败: {str(e)}")
            return {"score": 80.0, "detailed_feedback": "分析暂时不可用"}

    async def _analyze_department_accuracy_new(self, conversation_data: Dict[str, Any], patient_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析科室分配正确性 - 新方法"""
        try:
            suggested_department = conversation_data.get("suggested_department", "")
            correct_department = patient_info.get("correct_department", "")

            accuracy = 100.0 if suggested_department == correct_department else 70.0

            return {
                "score": accuracy,
                "correct_department": correct_department,
                "suggested_department": suggested_department,
                "accuracy_percentage": accuracy,
                "reasoning_quality": 85.0,
                "detailed_feedback": "科室分配基本合理。"
            }

        except Exception as e:
            logger.error(f"科室分配正确性分析失败: {str(e)}")
            return {"score": 80.0, "detailed_feedback": "分析暂时不可用"}

    async def _prepare_conversation_display_new(self, conversation_data: Dict[str, Any]) -> Dict[str, Any]:
        """准备对话记录展示 - 新方法"""
        try:
            messages = conversation_data.get("messages", [])

            formatted_messages = []
            for i, message in enumerate(messages):
                formatted_messages.append({
                    "round": i // 2 + 1,
                    "speaker": "医生" if message.get("message_type") == "doctor" else "患者",
                    "content": message.get("content", ""),
                    "timestamp": message.get("timestamp", "")
                })

            return {
                "total_rounds": len(messages) // 2,
                "messages": formatted_messages,
                "key_interactions": [],
                "summary": "医生与患者进行了充分的沟通。"
            }

        except Exception as e:
            logger.error(f"对话记录展示准备失败: {str(e)}")
            return {"total_rounds": 0, "messages": [], "summary": "对话记录暂时无法获取"}

    async def _generate_overall_assessment_new(
        self,
        emotional_value: Dict[str, Any],
        diagnosis_accuracy: Dict[str, Any],
        department_accuracy: Dict[str, Any],
        conversation_data: Dict[str, Any]
    ) -> tuple:
        """生成总体评估 - 新方法"""
        try:
            emotional_score = emotional_value.get("score", 0.0)
            diagnosis_score = diagnosis_accuracy.get("score", 0.0)
            department_score = department_accuracy.get("score", 0.0)

            overall_score = (emotional_score * 0.3 + diagnosis_score * 0.5 + department_score * 0.2)

            if overall_score >= 90:
                grade = "优秀"
            elif overall_score >= 80:
                grade = "良好"
            elif overall_score >= 70:
                grade = "中等"
            else:
                grade = "需要改进"

            recommendations = [
                "继续保持良好的沟通技巧",
                "在诊断过程中可以更加细致",
                "加强对患者情绪的关注和支持"
            ]

            return recommendations, overall_score, grade

        except Exception as e:
            logger.error(f"总体评估生成失败: {str(e)}")
            return ["需要进一步改进"], 70.0, "中等"

    async def _save_analysis_result_new(self, analysis_result: VirtualPatientAnalysisResult):
        """保存分析结果到数据库 - 新方法"""
        try:
            db = await self._get_db()
            await db.analysis_results.insert_one(analysis_result.dict())
            logger.info(f"分析结果保存成功: {analysis_result.session_id}")
        except Exception as e:
            logger.error(f"保存分析结果失败: {str(e)}")

    async def get_virtual_patient_analysis_result(self, session_id: str) -> Optional[VirtualPatientAnalysisResult]:
        """获取虚拟患者分析结果"""
        try:
            db = await self._get_db()
            result_data = await db.analysis_results.find_one({"session_id": session_id})

            if result_data:
                result_data.pop('_id', None)
                return VirtualPatientAnalysisResult(**result_data)
            return None

        except Exception as e:
            logger.error(f"获取分析结果失败: {str(e)}")
            return None

    async def get_conversation_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取对话数据"""
        try:
            session = await conversation_service.get_session(session_id)
            if not session:
                return None

            messages = await conversation_service.get_messages(session_id)

            return {
                "session_id": session_id,
                "messages": [msg.dict() for msg in messages],
                "total_rounds": session.current_round,
                "duration": 0,  # 计算实际时长
                "message_count": len(messages)
            }

        except Exception as e:
            logger.error(f"获取对话数据失败: {str(e)}")
            return None

    async def get_patient_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取患者信息"""
        try:
            session = await conversation_service.get_session(session_id)
            if not session:
                return None

            # 这里应该根据patient_id获取患者信息
            # 暂时返回模拟数据
            return {
                "patient_id": session.patient_id,
                "correct_diagnosis": "示例诊断",
                "correct_department": "内科"
            }

        except Exception as e:
            logger.error(f"获取患者信息失败: {str(e)}")
            return None

    async def get_analysis_sessions(self, limit: int = 20, offset: int = 0, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取分析会话列表"""
        try:
            db = await self._get_db()

            query = {}
            if status:
                query["status"] = status

            cursor = db.analysis_results.find(query).skip(offset).limit(limit)
            results = await cursor.to_list(length=limit)

            sessions = []
            for result in results:
                result.pop('_id', None)
                sessions.append({
                    "session_id": result.get("session_id"),
                    "analysis_timestamp": result.get("analysis_timestamp"),
                    "overall_score": result.get("overall_score"),
                    "grade": result.get("grade")
                })

            return sessions

        except Exception as e:
            logger.error(f"获取分析会话列表失败: {str(e)}")
            return []

    async def delete_analysis_result(self, session_id: str) -> bool:
        """删除分析结果"""
        try:
            db = await self._get_db()
            result = await db.analysis_results.delete_one({"session_id": session_id})
            return result.deleted_count > 0
        except Exception as e:
            logger.error(f"删除分析结果失败: {str(e)}")
            return False

# 全局服务实例
analysis_service = AnalysisService()
