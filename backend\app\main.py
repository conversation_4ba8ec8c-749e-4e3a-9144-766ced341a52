from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Any, Optional
import uvicorn
import os
import json
from datetime import datetime

# 导入自定义模块
from app.routes import analysis, health
from app.services import emotion_service

# 创建FastAPI应用实例
app = FastAPI(
    title="情绪分析助手API",
    description="情绪分析助手的后端API服务",
    version="0.1.0"
)

# 配置CORS中间件，允许前端访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制为特定域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含路由模块
app.include_router(health.router)
app.include_router(analysis.router, prefix="/api")

# 根路由
@app.get("/")
async def root():
    return {"message": "情绪分析助手API服务正在运行"}

# 启动服务器的入口点（直接运行此文件时使用）
if __name__ == "__main__":
    # 确定端口，优先使用环境变量中的端口
    port = int(os.environ.get("PORT", 8000))
    
    # 启动服务器
    uvicorn.run("app.main:app", host="0.0.0.0", port=port, reload=True)