import { createRouter, createWebHashHistory } from 'vue-router';

// 导入页面组件
const Home = () => import('./views/Home.vue');
const Analysis = () => import('./views/Analysis.vue');
const Result = () => import('./views/Result.vue');

// 创建路由实例
const router = createRouter({
  // 使用hash模式，适合Electron应用
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: Home,
      meta: { title: '情绪分析助手' }
    },
    {
      path: '/analysis',
      name: 'Analysis',
      component: Analysis,
      meta: { title: '情绪分析' }
    },
    {
      path: '/result',
      name: 'Result',
      component: Result,
      meta: { title: '分析结果' }
    },
    // 默认重定向到首页
    {
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ]
});

// 路由前置守卫，设置页面标题
router.beforeEach((to, from, next) => {
  // 设置窗口标题
  if (to.meta.title) {
    document.title = to.meta.title;
  }
  next();
});

export default router;